# macOS Setup Automation

This repository contains scripts and configuration files to automate the setup of a new macOS machine. It handles software installation, macOS system configuration, and various custom utility scripts.

## Quick Start

Run this in the terminal to set up a new machine:

```bash
cd ~ && rm -rf ~/dot-files && chmod 600 ~/Documents/id_rsa_carbonellpablo_git_ssh && GIT_SSH_COMMAND='ssh -i ~/Documents/id_rsa_carbonellpablo_git_ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no' <NAME_EMAIL>:carbonellpablo/dot-files.git ~/dot-files && chmod +x ~/dot-files/setup/setup.sh && cd ~/dot-files/setup && zsh setup.sh
```

## Software Installation

### Homebrew Bundle

Software installation is managed through a `Brewfile` at the root of the repository. This file uses Homebrew Bundle to declaratively define all software to be installed, organized by category:

- CLI tools
- Development environment
- Browsers
- Communication apps
- macOS improvements
- Security tools
- Other applications
- Mac App Store apps

To customize the software installation, edit the `Brewfile` in the repository root.

## macOS System Configuration

The macOS system preferences are configured through a modular structure located in `setup/macos-defaults/`:

- `system/` - General system settings and sudo-required configurations
- `security/` - Security and privacy settings
- `dock/` - Dock appearance and behavior
- `finder/` - Finder preferences and behaviors
- `input/` - Keyboard and trackpad settings
- `apps/` - Application-specific settings

Each domain is separated into individual files, making it easy to understand, maintain, and customize specific settings without affecting others.

### Customization

- **Computer Name**: Edit the `COMPUTER_NAME` variable in `setup/macos-defaults.sh`
- **macOS Settings**: Modify or add files in the appropriate subdirectory of `setup/macos-defaults/`
- **Software**: Edit the `Brewfile` to add or remove applications

## Maintenance Scripts

The `scripts/` directory contains several utility scripts for common operations:

- `mac_update` - Run to update your entire system (macOS, Homebrew, App Store, etc.)
- `dns-manager` - Manage DNS settings easily

## Creating a New Machine from Scratch

1. Install macOS on your new machine
2. Copy your SSH key to `~/Documents/id_rsa_carbonellpablo_git_ssh`
3. Run the quick start command above
