#!/bin/zsh

set -e # Exit immediately if a command exits with a non-zero status.

echo "Starting setup..."

# Ensure script is running from its own directory
cd "$(dirname "$0")"

source macos-defaults/macos-defaults.sh && echo "Macos defaults setup done"

source ../config/symlink-config.sh && echo "User config symlinked"

# Reload zsh to use the new config
source ~/.zshrc && echo "ZSH reloaded with new config"

source ./software/install.sh && echo "Software installed"

echo "Setup completed successfully!"