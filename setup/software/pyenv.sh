#!/bin/sh
# pyenv
if ! type pyenv > /dev/null 2>&1; then
  echo "Installing pyenv..."
  /bin/sh -c "$(curl -fsSL https://pyenv.run)"

  # Apply pyenv initialization
  export PYENV_ROOT="$HOME/.pyenv"
  export PATH="$PYENV_ROOT/bin:$PATH"
  eval "$(pyenv init --path)"
  eval "$(pyenv virtualenv-init -)"

  # Install the desired version of Python
  pyenv install 3.12.2

  # Set it as the default version
  pyenv global 3.12.2

  echo "Python setup with pyenv is complete."
else
  echo "pyenv is already installed."
fi
