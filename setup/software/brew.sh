#!/bin/bash

export ARCHFLAGS="-arch arm64"
HOMEBREW_INSTALL_PATH="$HOME/homebrew"

if [ ! -d "$HOMEBREW_INSTALL_PATH" ]; then
    # Install Homebrew
    echo "Installing homebrew to $HOMEBREW_INSTALL_PATH"
    mkdir -p "$HOMEBREW_INSTALL_PATH"
    NONINTERACTIVE=1 /bin/sh -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    # Set up Homebrew environment
    eval "$($HOMEBREW_INSTALL_PATH/bin/brew shellenv)"
    brew update --force --quiet

    # Fix permissions for zsh
    chmod -R go-w "$($HOMEBREW_INSTALL_PATH/bin/brew --prefix)/share/zsh"
else
    echo "Homebrew already installed."
fi
