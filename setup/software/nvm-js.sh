#!/bin/sh

if [ ! -d "$HOME/.nvm" ]; then
  echo "Installing NVM..."
  /bin/sh -c "$(curl -fsSL https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh)"

  export NVM_DIR="$HOME/.nvm"
  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
  [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

  # Install the latest LTS version of Node.js
  nvm install --lts --latest-npm --alias=default

  nvm use default

  nvm cache clear

  echo "Node.js setup and cleanup completed."
else
  echo "NVM is already installed."
fi
