#!/bin/zsh

# Ensure script is running from its own directory
cd "$(dirname "$0")"

echo "Applying macOS default settings..."

# You can customize your computer name here or leave empty to skip
export COMPUTER_NAME="mm"

# System settings (sudo required)
if [ -f "./macos-defaults/system/sudo.sh" ]; then
  source ./macos-defaults/system/sudo.sh
else
  echo "Warning: System sudo settings file not found"
fi

# General system settings
if [ -f "./macos-defaults/system/general.sh" ]; then
  source ./macos-defaults/system/general.sh
else
  echo "Warning: System general settings file not found"
fi

# Security and privacy settings
if [ -f "./macos-defaults/security/privacy.sh" ]; then
  source ./macos-defaults/security/privacy.sh
else
  echo "Warning: Security and privacy settings file not found"
fi

# Dock settings
if [ -f "./macos-defaults/dock/appearance.sh" ]; then
  source ./macos-defaults/dock/appearance.sh
else
  echo "Warning: Dock settings file not found"
fi

# Finder settings
if [ -f "./macos-defaults/finder/preferences.sh" ]; then
  source ./macos-defaults/finder/preferences.sh
else
  echo "Warning: Finder settings file not found"
fi

# Keyboard settings
if [ -f "./macos-defaults/input/keyboard.sh" ]; then
  source ./macos-defaults/input/keyboard.sh
else
  echo "Warning: Keyboard settings file not found"
fi

# Trackpad settings
if [ -f "./macos-defaults/input/trackpad.sh" ]; then
  source ./macos-defaults/input/trackpad.sh
else
  echo "Warning: Trackpad settings file not found"
fi

# Terminal app settings
if [ -f "./macos-defaults/apps/terminal.sh" ]; then
  source ./macos-defaults/apps/terminal.sh
else
  echo "Warning: Terminal settings file not found"
fi

# Add any additional app-specific settings here
# e.g., if [ -f "./macos-defaults/apps/safari.sh" ]; then
#         source ./macos-defaults/apps/safari.sh
#       fi

echo "All macOS default settings have been applied successfully."
echo "Note: Some changes may require a logout/restart to take effect."
