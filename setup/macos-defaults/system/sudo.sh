#!/bin/zsh

echo "Applying system settings that require sudo..."

# Set hostname (computer name)
if [[ -n "$COMPUTER_NAME" ]]; then
  echo "Setting computer name to $COMPUTER_NAME..."
  sudo scutil --set ComputerName "$COMPUTER_NAME"
  sudo scutil --set HostName "$COMPUTER_NAME"
  sudo scutil --set LocalHostName "$COMPUTER_NAME"
  sudo defaults write /Library/Preferences/SystemConfiguration/com.apple.smb.server NetBIOSName -string "$COMPUTER_NAME"
else
  echo "COMPUTER_NAME not set, skipping hostname configuration."
fi

# Restart automatically if the computer freezes
sudo systemsetup -setrestartfreeze on &>/dev/null

# Enable and configure the firewall
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setglobalstate on
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setblockall on
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setstealthmode on

# Disable transparency in the menu bar and elsewhere
sudo defaults write com.apple.universalaccess reduceTransparency -bool true

# Disable automatic login
sudo defaults write /Library/Preferences/com.apple.loginwindow autoLoginUser -string ""

# Screen saver and sleep settings
sudo pmset -a lidwake 1
sudo pmset -a displaysleep 15
sudo pmset -c sleep 15
sudo pmset -b sleep 15
sudo pmset -a hibernatemode 0

# Time and date settings
sudo defaults write /Library/Preferences/com.apple.timezone.auto Active -bool YES
sudo systemsetup -setusingnetworktime on

# Disable remote access and guest account
sudo /System/Library/CoreServices/RemoteManagement/ARDAgent.app/Contents/Resources/kickstart -deactivate -configure -access -off &>/dev/null || true
sudo defaults write /Library/Preferences/com.apple.loginwindow GuestEnabled -bool NO
sudo defaults write /Library/Preferences/com.apple.loginwindow SHOWOTHERUSERS_MANAGED -bool FALSE

# Spotlight indexing
sudo mdutil -i on / > /dev/null
sudo mdutil -E / > /dev/null

echo "System settings requiring sudo applied successfully." 