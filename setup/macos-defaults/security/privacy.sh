#!/bin/zsh

echo "Applying security and privacy settings..."

# Require password immediately after sleep or screen saver begins
defaults write com.apple.screensaver askForPassword -int 1
defaults write com.apple.screensaver askForPasswordDelay -int 0

# Enable Secure Keyboard Entry in Terminal.app
# See: https://security.stackexchange.com/a/47786/8918
defaults write com.apple.terminal SecureKeyboardEntry -bool true

# Enable FileVault (if not already enabled)
if [ "$(fdesetup status | grep -c "FileVault is On")" -eq 0 ]; then
  echo "Enabling FileVault..."
  # This will only queue it up - user will need to complete setup when prompted
  sudo fdesetup enable -user "$(whoami)"
fi

# Turn on firewall (if not already enabled)
if [ "$(sudo defaults read /Library/Preferences/com.apple.alf globalstate)" != "1" ]; then
  echo "Enabling firewall..."
  sudo defaults write /Library/Preferences/com.apple.alf globalstate -int 1
fi

echo "Security and privacy settings applied successfully." 