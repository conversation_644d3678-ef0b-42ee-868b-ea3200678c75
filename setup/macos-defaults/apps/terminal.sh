#!/bin/zsh

echo "Applying Terminal.app settings..."

# Only use UTF-8 in Terminal.app
defaults write com.apple.terminal StringEncodings -array 4

# Use a custom theme
# (This assumes you have the theme file in your configs)
osascript <<EOD
tell application "Terminal"
    set custom settings to settings set "Pro"
    set default settings to custom settings
    set startup settings to custom settings
end tell
EOD

# Enable Secure Keyboard Entry in Terminal.app
# See: https://security.stackexchange.com/a/47786/8918
defaults write com.apple.terminal SecureKeyboardEntry -bool true

# Disable the annoying line marks
defaults write com.apple.Terminal ShowLineMarks -int 0

echo "Terminal.app settings applied successfully." 