#!/bin/zsh

echo "Applying Dock appearance and behavior settings..."

# Set the size of the Dock icons
defaults write com.apple.dock tilesize -int 46

# Minimize windows into their application's icon
defaults write com.apple.dock minimize-to-application -bool true

# Enable magnification
defaults write com.apple.dock magnification -bool true
defaults write com.apple.dock largesize -int 64

# Minimize windows using scale effect
defaults write com.apple.dock mineffect -string "scale"

# Position the Dock on the left side of the screen
defaults write com.apple.dock orientation -string "left"

# Automatically hide and show the Dock
defaults write com.apple.dock autohide -bool true

# Make autohide animation faster
defaults write com.apple.dock autohide-delay -float 0.0
defaults write com.apple.dock autohide-time-modifier -float 0.3

# Don't show recent applications in Dock
defaults write com.apple.dock show-recents -bool false

# Show indicators for open applications
defaults write com.apple.dock show-process-indicators -bool true

# Restart Dock for changes to take effect
killall Dock

echo "Dock appearance and behavior settings applied successfully." 