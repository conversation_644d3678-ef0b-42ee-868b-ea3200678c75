name: Release Workflow

on:
  push:
    branches:
      - main

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: true

      - name: Generate Semantic Release Configuration
        run: |
          cat <<EOF > release.config.js
          module.exports = {
            branches: ["main"],
            plugins: [
              [
                "@semantic-release/commit-analyzer",
                {
                  releaseRules: [
                    { type: "feat", release: "minor" },
                    { type: "fix", release: "patch" },
                    { type: "chore", release: "patch" },
                    { type: "refactor", release: "patch" },
                    { type: "ci", release: "patch" },
                  ],
                  preset: "angular",
                },
              ],
              "@semantic-release/release-notes-generator",
              "@semantic-release/changelog",
              "@semantic-release/github"
            ]
          };
          EOF

      - name: Semantic Release
        uses: cycjimmy/semantic-release-action@v4
        with:
          extra_plugins: |
            @semantic-release/changelog
            @semantic-release/github
            @semantic-release/commit-analyzer
            @semantic-release/release-notes-generator
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Commit Changelog
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "GitHub Action"
          git add CHANGELOG.md
          git commit -m "chore: update CHANGELOG.md [skip ci]" || echo "No changes to commit"
          git push
