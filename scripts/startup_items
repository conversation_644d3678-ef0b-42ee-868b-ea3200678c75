#!/bin/bash

# Prompt the user to switch to 'm' user and execute the script with sudo privileges
if [[ "$(id -u)" -ne 0 ]]; then
    echo "Switching to user 'm' to execute the script with necessary privileges..."
    su m -c 'sudo bash '$0''
    exit $?
fi

echo "Script started. Listing non-Apple startup items..."

# Function to check and list non-Apple items
list_non_apple_items() {
    local path="$1"
    echo "Checking in: $path"

    # Find .plist files not within /System/Library (assuming these are Apple's)
    find "$path" -name "*.plist" ! -path "/System/Library/*" -exec sh -c '
        for plist do
            label=$(defaults read "$plist" Label 2>/dev/null)
            if [[ -n "$label" && ! "$plist" =~ com.apple. ]]; then
                echo "Non-Apple Item Found: $plist"
            fi
        done
    ' sh {} +
}

# Paths to check
paths_to_check=(
    "/Library/LaunchAgents"
    "/Library/LaunchDaemons"
    "/System/Library/LaunchAgents"
    "/System/Library/LaunchDaemons"
)

# Check system-wide items
for path in "${paths_to_check[@]}"; do
    if [[ -d $path ]]; then
        list_non_apple_items "$path"
    else
        echo "Path not found: $path"
    fi
done

# Check user-specific items
echo "Checking user-specific items..."
for user_dir in /Users/<USER>
    user_launch_agents="$user_dir/Library/LaunchAgents"
    if [[ -d $user_launch_agents ]]; then
        list_non_apple_items "$user_launch_agents"
    else
        echo "No LaunchAgents directory found for: $user_dir"
    fi
done


# List Login Items using AppleScript
echo "Listing Login Items for all users..."

# AppleScript to list Login Items. This needs to be run for each user.
list_login_items_script='
tell application "System Events"
    set loginItemList to name of login items
    set loginItemPathList to path of login items
    repeat with i from 1 to count loginItemList
        log "Name: " & item i of loginItemList & ", Path: " & item i of loginItemPathList
    end repeat
end tell
'

# Execute for the current user. Extend this to iterate over users if needed.
sudo osascript -e "$list_login_items_script"

echo "Script execution completed."
