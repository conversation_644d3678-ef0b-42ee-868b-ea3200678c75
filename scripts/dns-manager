#!/bin/bash

echo "Note: macOS /etc/hosts does not support wildcards. Each subdomain must be added individually."
echo

HOSTS_FILE="/etc/hosts"

validate_domain() {
    local domain=$1
    if [[ ! "$domain" =~ ^[a-zA-Z0-9][a-zA-Z0-9.-]+\.[a-zA-Z0-9.-]+$ ]]; then
        echo "Invalid domain format: $domain"
        return 1
    fi
    return 0
}

add_entry() {
    local ip=$1
    local domain=$2

    if ! validate_domain "$domain"; then
        return 1
    fi
    
    local escaped_domain=$(echo "$domain" | sed 's/\./\\./g')
    if ! grep -q "^$ip[[:space:]]*$escaped_domain\$" "$HOSTS_FILE"; then
        echo "$ip $domain" | sudo tee -a "$HOSTS_FILE" > /dev/null
        echo "Added: $ip $domain"
        return 0
    else
        echo "Entry already exists"
        return 1
    fi
}

remove_entry() {
    local domain=$1

    if ! validate_domain "$domain"; then
        return 1
    fi
    
    local escaped_domain=$(echo "$domain" | sed 's/\./\\./g')
    if grep -q "[[:space:]]$escaped_domain\$" "$HOSTS_FILE"; then
        sudo sed -i '' "/[[:space:]]$escaped_domain\$/d" "$HOSTS_FILE"
        echo "Removed entries for: $domain"
        return 0
    else
        echo "No entries found for: $domain"
        return 1
    fi
}

show_help() {
    echo "Usage:"
    echo "  dns-manager add <ip> <domain>     Add a DNS entry"
    echo "  dns-manager remove <domain>       Remove DNS entry for domain"
    echo "  dns-manager list                  List all entries"
    echo "  dns-manager help                  Show this help"
    echo
    echo "Note: Wildcards (*.example.com) are not supported by macOS /etc/hosts."
    echo "      Each subdomain must be added individually."
}

case "$1" in
    "add")
        if [ $# -ne 3 ]; then
            echo "Error: add requires IP and domain"
            show_help
            exit 1
        fi
        add_entry "$2" "$3"
        ;;
    "remove")
        if [ $# -ne 2 ]; then
            echo "Error: remove requires domain"
            show_help
            exit 1
        fi
        remove_entry "$2"
        ;;
    "list")
        grep -v '^#' "$HOSTS_FILE"
        ;;
    "help"|*)
        show_help
        ;;
esac
