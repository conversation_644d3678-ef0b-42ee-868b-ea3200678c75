#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e
# Consider pipe failures as script failures
set -o pipefail

# Load environment variables
source .env

# PostgreSQL binaries path
export PATH="$HOME/Applications/pgAdmin 4.app/Contents/SharedSupport:$PATH"

# Variables from .env
remoteDbUrl=$REMOTE_DATABASE_URL
localDbUrl=$LOCAL_DATABASE_URL
sshUserHost=$REMOTE_SSH_URL
sshKey=$REMOTE_SSH_KEY

# Parse database credentials from URLs
parse_pg_url() {
    echo $1 | sed -E 's/postgresql:\/\/([^:]+):([^@]+)@([^:]+):([0-9]+)\/([^?]+)\?schema=([^&]+)/\1 \2 \3 \4 \5 \6/'
}

# Assigning remote database parameters
remoteDbUser=$(parse_pg_url $remoteDbUrl | cut -d ' ' -f 1)
remoteDbPassword=$(parse_pg_url $remoteDbUrl | cut -d ' ' -f 2)
remoteDbHost=$(parse_pg_url $remoteDbUrl | cut -d ' ' -f 3)
remoteDbPort=$(parse_pg_url $remoteDbUrl | cut -d ' ' -f 4)
remoteDbName=$(parse_pg_url $remoteDbUrl | cut -d ' ' -f 5)
remoteDbSchema=$(parse_pg_url $remoteDbUrl | cut -d ' ' -f 6)

# Assigning local database parameters
localDbUser=$(parse_pg_url $localDbUrl | cut -d ' ' -f 1)
localDbPassword=$(parse_pg_url $localDbUrl | cut -d ' ' -f 2)
localDbHost=$(parse_pg_url $localDbUrl | cut -d ' ' -f 3)
localDbPort=$(parse_pg_url $localDbUrl | cut -d ' ' -f 4)
localDbName=$(parse_pg_url $localDbUrl | cut -d ' ' -f 5)
localDbSchema=$(parse_pg_url $localDbUrl | cut -d ' ' -f 6)


echo "Remote Host: $remoteDbHost"
echo "Remote User: $remoteDbUser"
echo "Remote Port: $remoteDbPort"
echo "Remote Database: $remoteDbName"

echo "Local Host: $localDbHost"
echo "Local User: $localDbUser"
echo "Local Port: $localDbPort"
echo "Local Database: $localDbName"


# Setup SSH key for tunneling
SSH_KEY_PATH="/tmp/id_rsa_temp"
echo "$sshKey" > $SSH_KEY_PATH
chmod 600 $SSH_KEY_PATH

# Use a less common port
localForwardingPort=55666


echo "Killing process using port $localForwardingPort..."
lsof -ti:$localForwardingPort | xargs kill -9 || true

echo "Trying to establish SSH tunnel on port $localForwardingPort..."
ssh -fN -L $localForwardingPort:"$remoteDbHost":5432 -i $SSH_KEY_PATH $sshUserHost && echo "SSH tunnel established." || exit

# Backup the remote database
echo "Starting backup from remote database..."
PGPASSWORD="$remoteDbPassword" pg_dump --format=c --large-objects --no-owner --no-privileges --no-tablespaces --no-unlogged-table-data --no-comments --no-publications --no-subscriptions --no-security-labels --no-toast-compression --no-table-access-method --inserts -h localhost -p $localForwardingPort -U "$remoteDbUser" -d "$remoteDbName" -f /tmp/db_backup.dump
echo "Database backup complete."

# terminate connections to the local database
echo "Disconnecting all connections to the local database..."
PGPASSWORD="$localDbPassword" psql -h localhost -p $localDbPort -U "$localDbUser" -d "postgres" -c "
SELECT pg_terminate_backend(pg_stat_activity.pid)
FROM pg_stat_activity
WHERE pg_stat_activity.datname = '$localDbName'
  AND pid <> pg_backend_pid();
"
echo "All connections terminated."

echo "Dropping existing local database..."
PGPASSWORD="$localDbPassword" psql -h localhost -p $localDbPort -U "$localDbUser" -d "postgres" -c "DROP DATABASE IF EXISTS \"$localDbName\";"
echo "Local database dropped."

# Create the local database
echo "Creating new local database..."
PGPASSWORD="$localDbPassword" psql -h localhost -p $localDbPort -U "$localDbUser" -d "postgres" -c "CREATE DATABASE \"$localDbName\";"
echo "Local database created."

# Restore the backup to the local database
echo "Starting restore to local database..."
PGPASSWORD="$localDbPassword" pg_restore --no-owner --no-privileges --no-tablespaces --no-comments --no-publications --no-subscriptions --no-security-labels --no-table-access-method -h localhost -p $localDbPort -U "$localDbUser" -d "$localDbName" /tmp/db_backup.dump
echo "Database restoration complete."

echo "Cleaning up..."
rm -f $SSH_KEY_PATH /tmp/db_backup.dump
echo "Clean-up done."

echo "Operation completed successfully."
