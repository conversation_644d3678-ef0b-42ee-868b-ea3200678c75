#!/bin/bash

echo "Checking for macOS System Updates..."
softwareupdate -ia --verbose || echo "macOS System Updates failed."

echo "Checking for App Store Updates..."
mas upgrade || echo "App Store Updates failed."

echo "Checking for Homebrew Package Updates..."
brew update || echo "Homebrew update failed."

# Retrieve a list of outdated formulas and casks
outdated_formulas=$(brew outdated --formula | awk '{print $1}')
outdated_casks=$(brew outdated --cask --greedy | awk '{print $1}')

# Upgrade each outdated formula individually
for formula in $outdated_formulas; do
    brew upgrade --force --quiet $formula || echo "$formula upgrade failed, continuing with next..."
done

# Upgrade each outdated cask individually
for cask in $outdated_casks; do
    # Use --force to ensure any required permissions or password prompts are avoided
    brew upgrade --force --cask --quiet $cask || echo "$cask upgrade failed, continuing with next..."
done

echo "OMZ updates.."
exec zsh && omz update || echo "OMZ update failed."

echo "Pyenv updates.."
cd ~/.pyenv && git pull || echo "Pyenv update failed."

echo "bun updates.."
bun upgrade || echo "bun update failed."

echo "node updates.."
export NVM_DIR="$([ -z "${XDG_CONFIG_HOME-}" ] && printf %s "${HOME}/.nvm" || printf %s "${XDG_CONFIG_HOME}/nvm")"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" || echo "nvm load failed."
nvm install "lts/*" --reinstall-packages-from="$(nvm current)" || echo "nvm install lts failed."
nvm install-latest-npm || echo "npm latest install failed."

echo "All updates have been checked and applied where available."
