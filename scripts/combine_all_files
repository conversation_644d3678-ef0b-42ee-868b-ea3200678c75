#!/bin/bash

# Function to print usage
print_usage() {
    echo "Usage: $0 <path_to_directory> [--skip <regex_pattern>]"
    echo "Example: $0 src --skip '*.json|*.test.ts'"
    exit 1
}

# Function to check if file is binary
is_binary() {
    if grep -Iq . "$1"; then
        return 1 # Text file
    else
        return 0 # Binary file
    fi
}

# Parse arguments
SKIP_PATTERN=""
SEARCH_DIR=""
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip)
            if [ -z "$2" ]; then
                echo "Error: --skip requires a pattern"
                print_usage
            fi
            SKIP_PATTERN=$2
            shift 2
            ;;
        *)
            if [ -z "$SEARCH_DIR" ]; then
                SEARCH_DIR=$1
            else
                echo "Error: Unexpected argument: $1"
                print_usage
            fi
            shift
            ;;
    esac
done

# Check if directory argument was provided
if [ -z "${SEARCH_DIR}" ]; then
    print_usage
fi

# Check if the directory exists
if [ ! -d "${SEARCH_DIR}" ]; then
    echo "Error: Directory '${SEARCH_DIR}' not found."
    exit 1
fi

# Function to process files
process_files() {
    local file=$1
    local extension=${file##*.}
    local comment_symbol

    if [ ! -f "${file}" ]; then
        return
    fi

    # Convert extension to lowercase using tr
    extension=$(echo "$extension" | tr '[:upper:]' '[:lower:]')

    # Check if file is binary
    if is_binary "${file}"; then
        echo "Skipping binary file: $file"
        return
    fi

    # Skip specific files and patterns
    # Image Files
    case "${extension}" in
        jpg|jpeg|png|gif|bmp|tiff|svg|webp|heic|ico)
            echo "Skipping image file: $file"
            return
            ;;
    esac

    # Font Files
    case "${extension}" in
        woff|woff2|ttf|otf|eot)
            echo "Skipping font file: $file"
            return
            ;;
    esac

    # Archive Files
    case "${extension}" in
        zip|tar|gz|bz2|7z|rar)
            echo "Skipping archive file: $file"
            return
            ;;
    esac

    # Lock Files
    if [[ "${file}" == *"bun.lockb" ]]; then
        echo "Skipping bun.lockb file: $file"
        return
    fi
    if [[ "${file}" == *"package-lock.json" || "${file}" == *"yarn.lock" ]]; then
        echo "Skipping lock file: $file"
        return
    fi

    # Skip files matching the user-provided pattern
    if [ ! -z "${SKIP_PATTERN}" ] && [[ "${file}" =~ ${SKIP_PATTERN} ]]; then
        echo "Skipping user-specified pattern file: $file"
        return
    fi

    # Determine the comment symbol based on the file extension
    case "${extension}" in
        py|sh|tf|rb|pl|bash|zsh)
            comment_symbol="#"
            ;;
        ts|js|jsx|tsx|java|c|cpp|cs|go|rs|swift|kt|php)
            comment_symbol="//"
            ;;
        html|css|scss|md|yaml|yml|json)
            comment_symbol="//"  # Adjust if necessary
            ;;
        *)
            comment_symbol="#"
            ;;
    esac

    # Print the relative file path
    echo "Processing: ${file}"

    # Append the relative file path as a comment to clipboard content
    echo "${comment_symbol} ${file}" >> "${CLIPBOARD_CONTENT}"

    # Append the content of the file to clipboard content
    cat "${file}" >> "${CLIPBOARD_CONTENT}"
    echo -e "\n" >> "${CLIPBOARD_CONTENT}"
}

# Temporary file to hold the content for the clipboard
CLIPBOARD_CONTENT=$(mktemp)

# Export the function and variables so they're available to find's -exec
export -f process_files
export -f is_binary
export CLIPBOARD_CONTENT
export SKIP_PATTERN

# Use tree to list all files respecting .gitignore
tree -if --gitignore "${SEARCH_DIR}" | while read -r file; do
    process_files "${file}"
done

# Check if any files were found and processed
if [ ! -s "${CLIPBOARD_CONTENT}" ]; then
    echo "Error: Nothing copied. Maybe dir is empty? or gitignore?"
    rm "${CLIPBOARD_CONTENT}"
    exit 1
fi

# Copy the file content to the clipboard using pbcopy
pbcopy < "${CLIPBOARD_CONTENT}"

# Cleanup temporary file
rm "${CLIPBOARD_CONTENT}"
echo "Content has been copied to the clipboard"