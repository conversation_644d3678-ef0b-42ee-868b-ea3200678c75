#!/bin/bash

# Define key location
KEY_PATH="/Users/<USER>/.ssh/id_rsa_carbonellpablo_git_ssh"

# Generate a new SSH key for GitHub
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f "$KEY_PATH" -N ""

# Ensure the .ssh directory exists and set the right permissions
chmod 700 /Users/<USER>/.ssh

# Set the right permissions for the private key
chmod 600 "$KEY_PATH"

# Print the public key to the terminal
echo "Your public key is:"
cat "$KEY_PATH.pub"
