#!/bin/sh

# Setup the PATH for pyenv binaries and shims
export PYENV_ROOT="$HOME/.pyenv"
export PATH="$PYENV_ROOT/bin:$PATH"

type -a pyenv > /dev/null && eval "$(pyenv init --path)"

export ARCHFLAGS="-arch arm64"

# Disable analytics
export HOMEBREW_NO_ANALYTICS=1

# disable hints
export HOMEBREW_NO_ENV_HINTS=1

# Prevent insecure redirects
export HOMEBREW_NO_INSECURE_REDIRECT=1

# Set Homebrew's cache location
export HOMEBREW_CACHE="$HOME/Library/Caches/Homebrew"

# Set Homebrew's logs location
export HOMEBREW_LOGS="$HOME/Library/Logs/Homebrew"

# Force color output
export HOMEBREW_COLOR=1

# Disable emoji in Homebrew's output
export HOMEBREW_NO_EMOJI=1

# Set the number of jobs `make` should run simultaneously
export HOMEBREW_MAKE_JOBS=99

# Specify the editor for editing formulae
export HOMEBREW_EDITOR="/usr/bin/nano"

# Pass command-line options to `brew cask` commands
export HOMEBREW_CASK_OPTS="--appdir=~/Applications"

# HOMEBREW_AUTOREMOVE
export HOMEBREW_AUTOREMOVE=1

export HOMEBREW_CLEANUP_MAX_AGE_DAYS=1

export HOMEBREW_CLEANUP_PERIODIC_FULL_DAYS=1

export HOMEBREW_UPGRADE_GREEDY=1

export HOMEBREW_BOOTSNAP=1