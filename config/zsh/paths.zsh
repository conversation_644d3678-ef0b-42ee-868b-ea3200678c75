prepend-path() {
  [ -d "$1" ] && PATH="$1:$PATH"
}

# Prepend new items to PATH (if directory exists)
prepend-path "/bin"
prepend-path "/sbin"
prepend-path "/usr/bin"
prepend-path "/usr/sbin"
prepend-path "/usr/local/bin"
prepend-path "$HOME/homebrew/bin"
prepend-path "$HOME/homebrew/sbin"
prepend-path "$HOME/homebrew/opt"
prepend-path "$HOME/homebrew/opt/mysql-client/bin"
prepend-path "$HOME/bin/"
prepend-path "$HOME/.bun/bin"
prepend-path "$HOME/Applications/Visual Studio Code.app/Contents/Resources/app/bin"
prepend-path "$HOME/google-cloud-sdk/bin"
prepend-path "$HOME/.docker/bin"
prepend-path "$HOME/dot-files/scripts"
prepend-path "$HOME/.codeium/windsurf/bin"
# Wrap up
export PATH