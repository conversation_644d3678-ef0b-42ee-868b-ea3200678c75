# Copy pwd to clipboard

alias cpwd="pwd|tr -d '\n'|pbcopy"

alias chrome="open -a /Applications/Google\ Chrome.app"

# Exclude macOS specific files in ZIP archives

alias zip="zip -x *.DS_Store -x *__MACOSX* -x *.AppleDouble*"

# Start screen saver

alias afk="open /System/Library/CoreServices/ScreenSaverEngine.app"

# Log off

alias logoff="/System/Library/CoreServices/Menu\ Extras/User.menu/Contents/Resources/CGSession -suspend"


# Reload native apps

alias killfinder="killall Finder"
alias killdock="killall Dock"
alias killmenubar="killall SystemUIServer NotificationCenter"
alias killos="killfinder && killdock && killmenubar"
alias killnode="pkill -f node"
alias killpython="pkill -f python"

# Show system information

alias displays="system_profiler SPDisplaysDataType"
alias cpu="sysctl -n machdep.cpu.brand_string"
alias ram="top -l 1 -s 0 | grep PhysMem"