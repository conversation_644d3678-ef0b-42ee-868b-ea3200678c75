#!/bin/zsh

backup_and_link() {
    local src=$1 dst=$2
    if [ -f "$dst" ]; then
        mv "$dst" "${dst}.backup"
    fi
    ln -sf "$src" "$dst"
}

backup_and_link "./zsh/.zshrc" "$HOME/.zshrc"
backup_and_link "./zsh/.zprofile" "$HOME/.zprofile"
backup_and_link "./zsh/alias.zsh" "$HOME/alias.zsh"
backup_and_link "./zsh/paths.zsh" "$HOME/paths.zsh"
backup_and_link "./node/.npmrc" "$HOME/.npmrc"

mkdir -p $HOME/.ssh # Ensure .ssh directory exists before linking
backup_and_link "./ssh/config" "$HOME/.ssh/config"
chmod -R +x $PWD/scripts

backup_and_link "./git/.gitconfig" "$HOME/.gitconfig"